'use client';

import React, { useState } from 'react';

const TicTacToe: React.FC = () => {
  const [board, setBoard] = useState(Array(9).fill(''));
  const [currentPlayer, setCurrentPlayer] = useState<'X' | 'O'>('X');
  const [winner, setWinner] = useState<string | null>(null);

  const handleMove = (index: number) => {
    if (board[index] !== '' || winner) return;

    const newBoard = [...board];
    newBoard[index] = currentPlayer;
    setBoard(newBoard);

    if (checkWin(newBoard)) {
      setWinner(currentPlayer);
    } else {
      setCurrentPlayer(currentPlayer === 'X' ? 'O' : 'X');
    }
  };

  const checkWin = (b: string[]): boolean => {
    const lines = [
      [0, 1, 2], [3, 4, 5], [6, 7, 8],
      [0, 3, 6], [1, 4, 7], [2, 5, 8],
      [0, 4, 8], [2, 4, 6],
    ];
    return lines.some(([a, b_, c]) =>
      b[a] !== '' && b[a] === b[b_] && b[a] === b[c]
    );
  };

  const reset = () => {
    setBoard(Array(9).fill(''));
    setCurrentPlayer('X');
    setWinner(null);
  };

  return (
    <div className="flex flex-col items-center justify-center mt-6">
      <h2 className="text-xl font-semibold mb-4">
        {winner ? `${winner} wins! 🎉` : `Turn: ${currentPlayer}`}
      </h2>

      <div className="grid grid-cols-3 gap-2">
        {board.map((cell, i) => (
          <div
            key={i}
            className="w-20 h-20 text-3xl font-bold flex items-center justify-center bg-gray-100 rounded-md shadow cursor-pointer active:bg-gray-200 transition"
            onTouchStart={() => handleMove(i)}
            onClick={() => handleMove(i)} // fallback for non-touch devices
          >
            {cell}
          </div>
        ))}
      </div>

      {winner && (
        <button
          onClick={reset}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition"
        >
          Play Again
        </button>
      )}
    </div>
  );
};

export default TicTacToe;
