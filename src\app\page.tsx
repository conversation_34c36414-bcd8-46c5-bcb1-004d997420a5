"use client";

import { useState, useEffect } from "react";
import { PomodoroDock, PomodoroMode } from "@/components/pomodoro-dock";
import { AREffects } from "@/components/ar-effects";
import { PomodoroTimer } from "@/components/pomodoro-timer";
import { YouTubeMusicSearch } from "@/components/youtube-music-search";
import { DateTimeDisplay } from "@/components/date-time-display";
import dynamic from 'next/dynamic';
const TicTacToe = dynamic(() => import('@/components/tic-tac-toe'), { ssr: false });

interface Particle {
  id: number;
  left: number;
  top: number;
  animationDelay: number;
  animationDuration: number;
}

export default function Home() {
  const [currentMode, setCurrentMode] = useState<PomodoroMode>("studying");
  const [particles, setParticles] = useState<Particle[]>([]);

  const handleModeChange = (mode: PomodoroMode) => {
    setCurrentMode(mode);
    console.log("Mode changed to:", mode);
  };

  // Generate particles on client side only
  useEffect(() => {
    const generatedParticles: Particle[] = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      left: Math.random() * 100,
      top: Math.random() * 100,
      animationDelay: Math.random() * 3,
      animationDuration: 2 + Math.random() * 3,
    }));
    setParticles(generatedParticles);
  }, []);

  const getModeContent = () => {
    switch (currentMode) {
      case "studying":
        return (
          <div className="text-center relative">
            {/* Holographic Title */}
            <div className="relativesu">
              
              <div className="absolute inset-0 text-6xl font-bold text-cyan-400/20 blur-sm">
                POMODORO
              </div>
            </div>

            <div className="text-center mb-8">
              <p className="text-xl text-cyan-300/80 font-mono mb-3">
                &gt; You have 2 choices today:
              </p>
              <div className="space-y-2">
                <p className="text-lg text-cyan-400 font-bold font-mono">
                  [1] Work hard
                </p>
                <p className="text-lg text-cyan-400 font-bold font-mono">
                  [2] Work SMART and hard
                </p>
              </div>
            </div>

            {/* Pomodoro Timer */}
            <PomodoroTimer />
          </div>
        );
      case "playing":
        
        return (
          <div className="text-center relative">
            <div className="relative mb-8">
              <h1 className="text-6xl font-bold bg-gradient-to-r from-green-400 via-emerald-500 to-green-600 bg-clip-text text-transparent mb-4 animate-pulse">
                VR PLAYGROUND
              </h1>
              <div className="absolute inset-0 text-6xl font-bold text-green-400/20 blur-sm">
                VR PLAYGROUND
              </div>
            </div>

            <p className="text-xl text-green-300/80 mb-8 font-mono">
              &gt; Loading virtual reality environment...
            </p>

            <div className="relative mt-12 p-8 bg-black/60 backdrop-blur-xl border border-green-500/30 rounded-2xl shadow-2xl">
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 via-transparent to-emerald-500/10 rounded-2xl"></div>
              <div className="relative z-10">
                <h2 className="text-3xl font-bold text-green-400 mb-6 font-mono">[BREAK PROTOCOL]</h2>
                <p className="text-green-300/70 text-lg leading-relaxed">
                  Reality simulation active. Stress levels: <span className="text-green-400 font-bold">MINIMAL</span>
                  <br />
                  Recharge sequence initiated.
                </p>
                <main className="p-6">
      <h1 className="text-2xl font-bold text-center mb-6">Rest Time 🧘</h1>
      <TicTacToe />
    </main>
              </div>
            </div>
          </div>
        );
      case "music":
        return (
          <div className="text-center relative">
            

            <p className="text-xl text-purple-300/80 mb-8 font-mono">
              &gt; Neural frequency synchronization active...
            </p>

            {/* YouTube Music Search Interface */}
            <div className="relative mt-12 p-8 bg-black/60 backdrop-blur-xl border border-purple-500/30 rounded-2xl shadow-2xl max-w-4xl mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-transparent to-violet-500/10 rounded-2xl"></div>
              <div className="relative z-10">
                <h2 className="text-3xl font-bold text-purple-400 mb-6 font-mono">[MUSIC SEARCH PROTOCOL]</h2>
                <YouTubeMusicSearch />
              </div>
            </div>
          </div>
        );     
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* AR/VR Effects */}
      <AREffects />

      {/* Animated Grid Background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-purple-500/5 to-pink-500/10"></div>
        <div
          className="absolute inset-0 opacity-30"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            animation: 'grid-move 20s linear infinite'
          }}
        ></div>
      </div>

      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {particles.map((particle) => (
          <div
            key={particle.id}
            className="absolute w-1 h-1 bg-cyan-400 rounded-full opacity-60 animate-pulse"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
              animationDelay: `${particle.animationDelay}s`,
              animationDuration: `${particle.animationDuration}s`
            }}
          ></div>
        ))}
      </div>

      {/* Date Time Display */}
      <DateTimeDisplay />

      {/* Main Content */}
      <main className="relative z-10 container mx-auto px-4 py-16 flex items-center justify-center min-h-screen">
        <div className="max-w-4xl w-full">
          {getModeContent()}
        </div>
      </main>

      {/* Cyberpunk Dock */}
      <PomodoroDock
        currentMode={currentMode}
        onModeChange={handleModeChange}
      />

      {/* Custom Styles */}
      <style jsx>{`
        @keyframes grid-move {
          0% { transform: translate(0, 0); }
          100% { transform: translate(50px, 50px); }
        }
      `}</style>
    </div>
  );
}
